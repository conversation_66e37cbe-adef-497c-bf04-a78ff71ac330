package ru.mos.school.student.portfolio_fct_additional_education_attendence.configuration;

import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
@ConditionalOnClass(AnyConfig.class)
public class KafkaConfig {
    private final Map<String, String> additionalEducationProperty;

    /**
     * Настройка лисенера кафки.
     * @return фабрика лисенера
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactoryAdditionalEducation() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactoryTeacherRating());
        return factory;
    }

    /**
     * Сборка настроек для кафка консьюмера.
     * @return фабрика консьюмера
     */
    @Bean
    public ConsumerFactory<String, String> consumerFactoryTeacherRating() {
        Map<String, Object> props = new HashMap<>();

        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, additionalEducationProperty.get("bootstrap-servers"));
        props.put(ConsumerConfig.GROUP_ID_CONFIG, additionalEducationProperty.get("group-id"));
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, additionalEducationProperty.get("max-poll-interval-ms"));
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, additionalEducationProperty.get("max-poll-records"));

        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG,
                additionalEducationProperty.get("properties-security-protocol"));
        props.put(SaslConfigs.SASL_MECHANISM, additionalEducationProperty.get("properties-sasl-mechanism"));
        props.put(SaslConfigs.SASL_JAAS_CONFIG, additionalEducationProperty.get("properties-sasl-jaas-config"));

        return new DefaultKafkaConsumerFactory<>(props);
    }
}
