{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name:  {{ .Values.ingress.name }}
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: "/$2"
spec:
  rules:
    - host: {{ .Values.ingress.url | quote }}
      http:
        paths:
          - pathType: {{ .Values.ingress.pathType }}
            path: {{ .Values.ingress.path }}(/|$)(.*)
            backend:
              service:
                name: {{ .Chart.Name }}
                port:
                  number: {{ .Values.ingress.servicePort }}
  {{ end }}
