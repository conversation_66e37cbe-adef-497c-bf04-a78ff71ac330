package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.Employment;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.RefService;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.Utils;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.EmploymentMapperService;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.dto.KafkaMessage;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class EmploymentMapperServiceImpl implements EmploymentMapperService {
    private final RefService refService;

    private static final Long DATA_SOURCE_ID = 1L; //по умолчанию 1
    private static final Long ACHIEVEMENT_ACTIVITY_FORMAT_ID = 1L; //Формат проведения. "1" (Очный) по умолчанию
    private static final String CREATOR = "kafka"; //по умолчанию

    @Override
    public Employment messageToEntityNew(@NonNull KafkaMessage kafkaMessage) {
        return Employment.builder()
                .personId(kafkaMessage.personId())
                .startDate(kafkaMessage.acquiredAt().toLocalDate())
                .sourceCode(refService.getDataSourceRef(DATA_SOURCE_ID))
                .eventId(kafkaMessage.eventId())
                .organizationId(kafkaMessage.organizationId())
                .name(kafkaMessage.achievementName())
                .categoryCode(refService.getSectionRef(kafkaMessage.achievementCategory()))
                .creationDate(LocalDateTime.now())
                .description(kafkaMessage.achievementDescription())
                .endDate(kafkaMessage.activityCompletedAt())
                .creatorId(CREATOR)
                .achievementActivityFormatCode(
                        refService.getAchievementActivityFormatRef(ACHIEVEMENT_ACTIVITY_FORMAT_ID))
                .hashCode(Utils.getMd5Hash(kafkaMessage.toString()))
                .isImport(false)
                .isDelete(false)
                .build();
    }
}
