package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation;

import lombok.NonNull;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.Employment;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.dto.KafkaMessage;

public interface EmploymentMapperService {

    /**
     * Маппинг распаршенного сообщения в энтити БД.
     * @param kafkaMessage распаршенное сообщение
     * @return энтити БД
     */
    Employment messageToEntityNew(@NonNull KafkaMessage kafkaMessage);
}
