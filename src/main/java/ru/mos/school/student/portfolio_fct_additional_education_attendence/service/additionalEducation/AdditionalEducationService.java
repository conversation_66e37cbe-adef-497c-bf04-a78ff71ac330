package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.NonNull;
import org.springframework.messaging.support.GenericMessage;

public interface AdditionalEducationService {

    /**
     * Процесс парсинга, формирования, валидации записи полученной из топика и сохранения в БД.
     * @param message сырое сообщение из топика
     * @throws JsonProcessingException ошибка обработки Json
     */
    void process(@NonNull GenericMessage<String> message) throws JsonProcessingException;
}
