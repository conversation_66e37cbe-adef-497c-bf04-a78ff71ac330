package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation;

import lombok.NonNull;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.dto.KafkaMessage;

public interface ValidationService {

    /**
     * Запуск валидации полей вручную.
     * @param message распаршенное сообщение из топика
     * @param kafkaMessageProcessedId kafkaMessageProcessedId
     */
    void validateFields(@NonNull KafkaMessage message, @NonNull Long kafkaMessageProcessedId);
}
