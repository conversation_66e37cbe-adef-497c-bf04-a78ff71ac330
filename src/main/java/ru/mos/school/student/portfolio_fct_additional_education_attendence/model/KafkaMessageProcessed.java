package ru.mos.school.student.portfolio_fct_additional_education_attendence.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;

@Getter
@Setter
@RequiredArgsConstructor
@Entity
public class KafkaMessageProcessed extends AbstractEntity {

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private JsonNode message;

    private Long kafkaOffset;

    private String kafkaKey;

    @Column(name = "md5_hash")
    private String md5Hash;

    @CreationTimestamp
    private LocalDateTime parseDate;

    private Long duplicateMessageId;
}
