package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaConsumerService {
    private final AdditionalEducationService additionalEducationService;

    /**
     * Получение сообщения из топика.
     * @param message строка=сообщение, полученное из топика кафки
     */
    @KafkaListener(topics = "${additional-education.kafka.topics}",
            groupId = "${additional-education.kafka.group-id}",
            autoStartup = "${additional-education.kafka.enable}",
            containerFactory = "kafkaListenerContainerFactoryAdditionalEducation")
    public void onMessage(GenericMessage<String> message) {

        try {
            int i = message.hashCode();
            log.info("Старт обработки документа, хэш = {} = {}", i, message);
            additionalEducationService.process(message);
            log.info("Окончание обработки документа = {}", i);
        } catch (Exception e) {
            log.error(String.format("Непредвиденная ошибка, %s", e.getMessage()), e);
        }

    }
}
