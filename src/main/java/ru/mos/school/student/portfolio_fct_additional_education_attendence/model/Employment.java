package ru.mos.school.student.portfolio_fct_additional_education_attendence.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.AchievementActivityFormatRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.DataSourceRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.SectionRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.SportKindRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.SubcategoryRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.TourismKindRef;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Builder
@Getter
@Setter
@RequiredArgsConstructor
@AllArgsConstructor
@Entity
public class Employment extends AbstractEntity {

    @Column(name = "person_id", nullable = false)
    private String personId;

    @Column(name = "event_id")
    private String eventId;

    @Column(name = "organization_id")
    private String organizationId;

    @ManyToOne
    @JoinColumn(name = "category_code", nullable = false)
    private SectionRef categoryCode;

    @ManyToOne
    @JoinColumn(name = "source_code", nullable = false)
    private DataSourceRef sourceCode;

    @Column(name = "creator_id", nullable = false)
    private String creatorId;

    @ManyToOne
    @JoinColumn(name = "type_code", nullable = false)
    private SectionRef typeCode;

    @ManyToOne
    @JoinColumn(name = "subcategory_code")
    private SubcategoryRef subcategoryCode;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    @ManyToOne
    @JoinColumn(name = "achievement_activity_format_code", nullable = false)
    private AchievementActivityFormatRef achievementActivityFormatCode;

    @Column(name = "subjects_code")
    private String subjectsCode;

    @Column(name = "description")
    private String description;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "file_references")
    private String fileReferences;

    @Column(name = "linked_object_ids")
    private String linkedObjectIds;

    @ManyToOne
    @JoinColumn(name = "sport_kind_code")
    private SportKindRef sportKindCode;

    @Column(name = "age_limit")
    private String ageLimit;

    @Column(name = "location")
    private String location;

    @ManyToOne
    @JoinColumn(name = "tourism_kind_code")
    private TourismKindRef tourismKindCode;

    @Column(name = "creation_kind_code")
    private String creationKindCode;

    @CreatedDate
    @Column(name = "creation_date", nullable = false, updatable = false)
    private LocalDateTime creationDate;

    @ManyToOne
    @JoinColumn(name = "data_kind", nullable = false)
    private SectionRef dataKind;

    @Column(name = "discipline_code")
    private String disciplineCode;

    @Column(name = "subspecies")
    private String subspecies;

    @Column(name = "hash_code")
    private String hashCode;

    @Column(name = "is_import", nullable = false)
    private Boolean isImport;

    @Column(name = "is_delete", nullable = false)
    private Boolean isDelete;

    @LastModifiedDate
    @Column(name = "edit_date")
    private LocalDateTime editDate;
}