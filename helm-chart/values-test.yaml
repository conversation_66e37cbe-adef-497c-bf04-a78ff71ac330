env: test
tier: backend

deployment:
  deploymentAnnotations:
  replicaCount: 1
  image:
    repository: harbor-edu.mos.ru/mes-portfolio/portfolio-fct-additional-education-attendence
    tag: "latest"
    pullPolicy: IfNotPresent
  port: 8080
  #   resources:
  #     #limits:
  #     #  cpu: 2
  #     #  memory: 2048Mi
  #     requests:
  #       cpu: 1
  #       memory: 2Gi
  restartPolicy: Always
  volume: |-
    - name: {{ .Chart.Name }}
      configMap:
        name: {{ .Chart.Name }}
        defaultMode: 0777
        items:
          - key: run.sh
            path: run.sh
  volumeMounts: |-
    - name: {{ .Chart.Name }}
      mountPath: /opt/run.sh
      subPath: run.sh
  livenessProbe:
    path: /actuator/health/
    port: 8080
    initDelay: 30
    failThreshold: 3
  readinessProbe:
    path: /actuator/health/
    port: 8080
    initDelay: 30
    failThreshold: 3
    readnessPeriod: 5

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

ingress:
  enabled: false
  name: additional-education
  url: mes-kubernetes-test.mos.ru
  pathType: Prefix
  path: /additional-education/v1
  servicePort: 80

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

configmap:
  application:
    JAR_APP_PATH: /opt/additionalEducation.jar
    SERVER_PORT: 8080
    DATASOURCE_HOST: mesdb-cmnbeta01t
    DATASOURCE_PORT: 5443
    DATASOURCE_DB: portfolio
    DATASOURCE_USERNAME: portfolio
    DATASOURCE_PASSWORD: portfolio
    DATASOURCE_SCHEMA: portfolio
    KAFKA_ENABLE: true
    KAFKA_SERVERS: ***********:9092,***********:9092,***********:9092
    KAFKA_TOPICS: education.fct.additional-education-attendence.0
    KAFKA_GROUP_ID: portfolio_group
    KAFKA_USERNAME: portfolio
    KAFKA_PASSWORD: portfolio
    KAFKA_MAX_POLL_INTERVAL: 300000
    KAFKA_MAX_POLL_RECORDS: 500