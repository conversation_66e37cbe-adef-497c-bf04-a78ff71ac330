package ru.mos.school.student.portfolio_fct_additional_education_attendence.service;

import lombok.NonNull;
import lombok.experimental.UtilityClass;
import org.springframework.util.DigestUtils;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.error.NotFoundException;

import java.nio.charset.StandardCharsets;

@UtilityClass
public class Utils {

    /**
     * Получение номерного кода категории по буквенному наименованию.
     * @param achievementCategory буквенное наименование
     * @return номерной код категории
     */
    public static Long getCategoryCode(@NonNull String achievementCategory) {
        return switch (achievementCategory.toLowerCase().trim()) {
            case "спорт" -> 3L;
            case "культура" -> 4L;
            case "наука и техника", "гуманитарные науки" -> 2L;
            case "военно-патриотическая деятельность" -> 6L;
            default -> throw new NotFoundException(
                    String.format("Неизвестное achievementCategory: %s", achievementCategory)
            );
        };
    }

    /**
     * Получение md5 хэша строки(сообщения).
     * @param s сообщение в виде строки
     * @return md5 хэш
     */
    public static String getMd5Hash(@NonNull String s) {
        return DigestUtils.md5DigestAsHex(s.getBytes(StandardCharsets.UTF_8));
    }
}
