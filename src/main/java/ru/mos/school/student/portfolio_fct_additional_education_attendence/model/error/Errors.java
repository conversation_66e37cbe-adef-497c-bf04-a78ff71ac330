package ru.mos.school.student.portfolio_fct_additional_education_attendence.model.error;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum Errors {
    E701(500, "Ошибка БД"),
    E704(500, "Параметры переданы некорректно"),
    E750(500, "Объект с такими параметрами уже существует");

    private final int code;
    private final String description;

    /**
     * Выброс ошибки.
     * @param args аргументы.
     */
    public void thr(Object... args) {
        throw new CodifiedException(this, String.format(this.description, args));
    }

    @Getter
    @AllArgsConstructor
    @RequiredArgsConstructor
    public static class CodifiedException extends RuntimeException {
        private final Errors error;
        private String msg;

        /**
         * Формирование сообщения ошибки.
         * @return строка ошибки.
         */
        public String getMsg() {
            return error.name() + ": " + (msg != null ? msg : error.getDescription());
        }

        @Override
        public String getMessage() {
            return getMsg();
        }
    }
}
