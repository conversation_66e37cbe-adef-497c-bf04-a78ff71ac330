package ru.mos.school.student.portfolio_fct_additional_education_attendence.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.Employment;

import java.util.List;

public interface EmploymentRepository extends JpaRepository<Employment, Long> {

    /**
     * Получение списка сущностей по параметрам.
     * @param personId personId
     * @param eventId eventId
     * @param isDelete isDelete удалена ли запись
     * @return список Employment
     */
    List<Employment> findAllByPersonIdAndEventIdAndIsDelete(String personId, String eventId, boolean isDelete);
}