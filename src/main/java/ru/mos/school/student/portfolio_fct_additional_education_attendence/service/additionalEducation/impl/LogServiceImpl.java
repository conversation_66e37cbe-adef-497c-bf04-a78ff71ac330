package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.stereotype.Service;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.AbstractEntity;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.error.Errors;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.KafkaMessageProcessed;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.KafkaReaderLog;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.error.KafkaHeaderParseException;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.repository.KafkaMessageProcessedRepository;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.repository.KafkaReaderLogRepository;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.Utils;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.LogService;

import static java.time.LocalDateTime.now;

@Service
@RequiredArgsConstructor
public class LogServiceImpl implements LogService {
    private final KafkaMessageProcessedRepository kafkaMessageProcessedRepository;
    private final KafkaReaderLogRepository kafkaReaderLogRepository;

    private final ObjectMapper objectMapper;

    @Override
    public KafkaMessageProcessed buildKafkaMessageProcessed(@NonNull GenericMessage<String> message)
            throws JsonProcessingException {
        KafkaMessageProcessed kafkaMessageProcessed = new KafkaMessageProcessed();
        ObjectMapper mapper = new ObjectMapper();
        JsonNode json = mapper.readTree(message.getPayload());
        kafkaMessageProcessed.setMd5Hash(Utils.getMd5Hash(message.getPayload()));
        kafkaMessageProcessed.setKafkaOffset(getOffsetHeaderValue(message.getHeaders()));
        kafkaMessageProcessed.setMessage(json);
        return kafkaMessageProcessedRepository.save(kafkaMessageProcessed);
    }

    @Override
    public <T extends AbstractEntity> void buildKafkaLogOk(String personId, T entity, Long kafkaMessageProcessedId) {
        KafkaReaderLog log = new KafkaReaderLog();
        log.setPersonId(personId);
        log.setLogDateTime(now());
        log.setLogResult("OK");
        log.setEntityId(entity.getId());
        log.setEntityType(entity.getClass().getSimpleName());
        log.setSourceCode(18);
        log.setMessageId(kafkaMessageProcessedId);
        kafkaReaderLogRepository.save(log);
    }

    @Override
    public void buildKafkaLogFail(Boolean isTrue, Errors error, String personId, Long kafkaMessageProcessedId) {
        if (Boolean.TRUE.equals(isTrue)) {
            return;
        }
        KafkaReaderLog log = new KafkaReaderLog();
        log.setLogResult("FAIL");
        log.setErrorLogMessage(error.getDescription());
        log.setPersonId(personId);
        log.setLogDateTime(now());
        log.setSourceCode(18);
        log.setMessageId(kafkaMessageProcessedId);
        kafkaReaderLogRepository.save(log);
        error.thr();
    }

    /**
     * Парсинг header value.
     * @param headers список хэдеров сообщения
     * @return десериализованный объект
     */
    private Long getOffsetHeaderValue(MessageHeaders headers) {
        try {
            Object headerValue = headers.get(KafkaHeaders.OFFSET);
            String json = (headerValue instanceof byte[] byteValue)
                    ? new String(byteValue)
                    : objectMapper.writeValueAsString(headerValue);

            return objectMapper.readValue(json, Long.class);
        } catch (Exception e) {
            throw new KafkaHeaderParseException(String.format("Ошибка получения header: %s", KafkaHeaders.OFFSET), e);
        }
    }
}
