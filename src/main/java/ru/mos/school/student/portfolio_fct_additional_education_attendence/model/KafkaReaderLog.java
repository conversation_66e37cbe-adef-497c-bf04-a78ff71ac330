package ru.mos.school.student.portfolio_fct_additional_education_attendence.model;

import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@RequiredArgsConstructor
@Entity
public class KafkaReaderLog extends AbstractEntity {

    private String personId;

    private LocalDateTime logDateTime;

    private String logResult;

    private Integer actionTypeCode;

    private Integer actionKindCode;

    private Long entityId;

    private String entityType;

    private Integer sourceCode;

    private String errorLogMessage;

    private Long messageId;
}