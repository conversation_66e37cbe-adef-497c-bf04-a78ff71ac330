package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.error.NotFoundException;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.AchievementActivityFormatRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.DataSourceRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.SectionRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.repository.AchievementActivityFormatRefRepository;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.repository.DataSourceRefRepository;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.repository.SectionRefRepository;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.Utils;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.RefService;

@Service
@RequiredArgsConstructor
public class RefServiceImpl implements RefService {
    private final DataSourceRefRepository dataSourceRefRepository;
    private final SectionRefRepository sectionRefRepository;
    private final AchievementActivityFormatRefRepository achievementActivityFormatRefRepository;

    @Override
    public DataSourceRef getDataSourceRef(@NonNull Long dataSourceId) {
        return dataSourceRefRepository.findById(dataSourceId)
                .orElseThrow(() -> getError(DataSourceRef.class, dataSourceId));
    }

    @Override
    public AchievementActivityFormatRef getAchievementActivityFormatRef(@NonNull Long achievementActivityFormatCode) {
        return achievementActivityFormatRefRepository.findById(achievementActivityFormatCode)
                .orElseThrow(() -> getError(AchievementActivityFormatRef.class, achievementActivityFormatCode));
    }

    @Override
    public SectionRef getSectionRef(String achievementCategory) {
        Long categoryCode = Utils.getCategoryCode(achievementCategory);
        return sectionRefRepository.findById(categoryCode)
                .orElseThrow(() -> getError(SectionRef.class, categoryCode));
    }

    private NotFoundException getError(Class<?> entity, Long id) {
        return new NotFoundException(
                String.format("Не найдено сущности %s в БД по id: %s", entity.getSimpleName(), id));

    }
}
