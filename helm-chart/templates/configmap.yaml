apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "name" . }}
  namespace: {{ .Values.namespace }}
data:
  {{- with .Values.configmap.application }}
  run.sh: |
    #!/bin/bash
    clear
    # application jar path
    JAR_APP_PATH={{ .JAR_APP_PATH }} \
    # env conf variables
      SERVER_PORT={{ .SERVER_PORT }} \
      DATASOURCE_HOST={{ .DATASOURCE_HOST }} \
      DATASOURCE_PORT={{ .DATASOURCE_PORT }} \
      DATASOURCE_DB={{ .DATASOURCE_DB }} \
      DATASOURCE_USERNAME={{ .DATASOURCE_USERNAME }} \
      DATASOURCE_PASSWORD={{ .DATASOURCE_PASSWORD }} \
      DATASOURCE_SCHEMA={{ .DATASOURCE_SCHEMA }} \
      KAFKA_ENABLE={{ .KAFKA_ENABLE }} \
      KAFKA_SERVERS={{ .KAFKA_SERVERS }} \
      KAFKA_TOPICS={{ .KAFKA_TOPICS }} \
      KAFKA_GROUP_ID={{ .KAFKA_GROUP_ID }} \
      KAFKA_USERNAME={{ .KAFKA_USERNAME }} \
      KAFKA_PASSWORD={{ .KAFKA_PASSWORD }} \
      KAFKA_MAX_POLL_INTERVAL={{ .KAFKA_MAX_POLL_INTERVAL }} \
      KAFKA_MAX_POLL_RECORDS={{ .KAFKA_MAX_POLL_RECORDS }} \
      JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 \
      java -jar ${JAR_APP_PATH}
  {{ end }}
