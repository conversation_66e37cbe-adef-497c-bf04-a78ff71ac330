package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.impl;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.error.Errors;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.LogService;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.ValidationService;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.dto.KafkaMessage;

import java.util.Set;

@Service
@RequiredArgsConstructor
public class ValidationServiceImpl implements ValidationService {
    private final Validator validator;
    private final LogService logService;

    @Override
    public void validateFields(@NonNull KafkaMessage message, @NonNull Long kafkaMessageProcessedId) {
        Set<ConstraintViolation<KafkaMessage>> messageValidations = validator.validate(message);
        logService.buildKafkaLogFail(messageValidations.isEmpty(), Errors.E704, null, kafkaMessageProcessedId);
    }
}
