package ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@RequiredArgsConstructor
@MappedSuperclass
public abstract class RefEntity extends AbstractRefEntity<Short> {

    @Column(name = "value", nullable = false)
    private String value;
}
