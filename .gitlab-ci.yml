include:
  - remote: "https://gitlab-edu.mos.ru/api/v4/projects/1825/repository/files/.build.gitlab-ci.yaml/raw?ref=master&private_token=${GITLAB_TOKEN}&.yaml"

stages:
  - helm-lint
  - publish
  - deploy-dev
  - deploy-test
  - deploy-prod

variables:
  RELEASE: $DOCKER_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA
  HELM_EXPERIMENTAL_OCI: 1

helm-lint:
  stage: helm-lint
  image:
    name: harbor-edu.mos.ru/docker_proxy_cod/alpine/helm:3.9.0
    entrypoint: ["/bin/sh", "-c"]
  script:
    - |
      for v in helm-chart/values* ; do
      helm lint helm-chart -f "$v"
      done
  tags:
    - k8s-dev-sitronics

build:
  stage: publish
  image:
    name: gcr.io/kaniko-project/executor:v1.8.0-debug
    entrypoint: [""]
  script:
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$DOCKER_REGISTRY\":{\"auth\":\"$(echo -n ${DOCKER_USER}:${DOCKER_PASSWORD} | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor --image-fs-extract-retry=10 --push-retry=5 --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/Dockerfile --destination $RELEASE
  tags:
    - k8s-dev-sitronics

helm-publish:
  stage: publish
  image: jerbob92/helm-push
  script:
    - helm repo add --username ${HELM_REGISTRY_USER} --password ${HELM_REGISTRY_TOKEN} mes-helm ${HELM_REGISTRY_IP}
    - helm cm-push helm-chart mes-helm
  tags:
    - k8s-dev-sitronics

deploy-dev:
  stage: deploy-dev
  image: harbor-edu.mos.ru/docker_proxy_cod/alpine/helm:3.9.0
  when: manual
  script:
    - helm upgrade --set deployment.image.tag=${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA} -i -n mes-portfolio ${CI_PROJECT_NAME} ./helm-chart -f ./helm-chart/values-dev.yaml
  tags:
    - k8s-dev

telegram-dev:
  needs: [ "deploy-dev" ]
  stage: deploy-dev
  image:
    name: harbor-edu.mos.ru/docker_proxy_cod/curlimages/curl:latest
    entrypoint: [ "" ]
  script:
    - sh ./notify.sh
  tags:
    - k8s-dev-sitronics
  variables:
    CLUSTER: "dev"

deploy-test:
  stage: deploy-test
  image: harbor-edu.mos.ru/docker_proxy_cod/alpine/helm:3.9.0
  when: manual
  script:
    - helm upgrade --set deployment.image.tag=${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA} -i -n mes-portfolio ${CI_PROJECT_NAME} ./helm-chart -f ./helm-chart/values-test.yaml
  tags:
    - k8s-test

telegram-test:
  needs: [ "deploy-test" ]
  stage: deploy-test
  image:
    name: harbor-edu.mos.ru/docker_proxy_cod/curlimages/curl:latest
    entrypoint: [ "" ]
  script:
    - sh ./notify.sh
  tags:
    - k8s-dev-sitronics
  variables:
    CLUSTER: "test"

deploy-prod:
  stage: deploy-prod
  image: harbor-edu.mos.ru/docker_proxy_cod/alpine/helm:3.9.0
  when: manual
  script:
    - helm upgrade --set deployment.image.tag=${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA} -i -n mes-portfolio ${CI_PROJECT_NAME} ./helm-chart -f ./helm-chart/values-prod.yaml
  tags:
    - k8s-prod

telegram-prod:
  needs: [ "deploy-prod" ]
  stage: deploy-prod
  image:
    name: harbor-edu.mos.ru/docker_proxy_cod/curlimages/curl:latest
    entrypoint: [ "" ]
  script:
    - sh ./notify.sh
  tags:
    - k8s-dev-sitronics
  variables:
    CLUSTER: "Production"