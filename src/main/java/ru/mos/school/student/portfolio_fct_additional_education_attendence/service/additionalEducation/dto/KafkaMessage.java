package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.dto;

import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;

public record KafkaMessage(
        @NotNull String personId,
        @NotNull LocalDateTime acquiredAt,
        @NotNull String eventId,
        @NotNull String organizationId,
        @NotNull String achievementName,
        @NotNull String achievementCategory,
        String achievementDescription,
        LocalDate activityCompletedAt
) {
}
