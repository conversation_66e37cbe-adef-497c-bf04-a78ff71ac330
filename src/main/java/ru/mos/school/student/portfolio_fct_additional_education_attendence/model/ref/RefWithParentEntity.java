package ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@RequiredArgsConstructor
@MappedSuperclass
public abstract class RefWithParentEntity extends RefEntity {
    @Column(name = "parent_id")
    private Short parentId;
}
