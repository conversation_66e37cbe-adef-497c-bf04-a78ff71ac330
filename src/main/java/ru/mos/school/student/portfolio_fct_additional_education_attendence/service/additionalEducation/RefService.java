package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation;

import lombok.NonNull;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.AchievementActivityFormatRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.DataSourceRef;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref.SectionRef;

public interface RefService {
    /**
     * Получение объекта справочника по id.
     * @param dataSourceId dataSourceId
     * @return объект справочника
     */
    DataSourceRef getDataSourceRef(@NonNull Long dataSourceId);

    /**
     * Получение объекта справочника по id.
     * @param achievementActivityFormatCode dataSourceId
     * @return объект справочника
     */
    AchievementActivityFormatRef getAchievementActivityFormatRef(@NonNull Long achievementActivityFormatCode);

    /**
     * Получение объекта справочника по id.
     * @param achievementCategory dataSourceId
     * @return объект справочника
     */
    SectionRef getSectionRef(String achievementCategory);
}
