# Chart – это пакет Helm. Он содержит описания ресурсов, необходимые для запуска приложения, инструмента или службы внутри кластера Kubernetes
#Версия апи взаимодействия helm с k8s (always 2)
apiVersion: v2
#Имя чарта (любое чтобы было сразу понятно что за продукт)
name: portfolio-fct-additional-education-attendence
#Описание чарта
description: portfolio-fct-additional-education-attendence Helm chart for Kubernetes
#Вид чарта (application or library)
type: application
#Версия чарта (пока не версионируется - ставим любую)
version: 0.1.0
#Версия приложения(влияет на деплой из шаблона)
appVersion: "1.0.0"