plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.4'
	id 'io.spring.dependency-management' version '1.1.7'
	id 'checkstyle'
}

group = 'ru.mos.school.student'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

repositories {
	mavenCentral()
}

dependencies {
	// Spring Boot стартеры
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.kafka:spring-kafka'

	// База данных
	runtimeOnly 'org.postgresql:postgresql'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	// Конфигурация
	annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

	// Метрики
	implementation 'io.micrometer:micrometer-registry-prometheus'

	// Тестирование
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.kafka:spring-kafka-test'
	testImplementation 'org.testcontainers:junit-jupiter'
	testImplementation 'org.testcontainers:postgresql'
	testImplementation 'org.testcontainers:kafka'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

	implementation 'com.fasterxml.jackson.core:jackson-databind:2.19.0'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8'
}

tasks.named('test') {
	useJUnitPlatform()
}

bootJar {
	archiveFileName = "additionalEducation.jar"
}

checkstyle {
	toolVersion = '10.12.5'
	configFile = file("${rootDir}/config/checkstyle/checkstyle.xml")
	ignoreFailures = false
	maxWarnings = 0
}

tasks.register('copySh', Copy) {
	from 'run.sh'
	into 'build/libs/'

	filePermissions {
		user {
			read = true
			execute = true
			write = true
		}
		group {
			read = true
			execute = true
		}
		other {
			read = true
			execute = true
		}
	}

	doFirst {
		if (!file('run.sh').exists()) {
			throw new GradleException("File run.sh not found in project root")
		}
	}
}

tasks.register('release') {
	dependsOn tasks.named('build')
	dependsOn tasks.named('copySh')
	tasks.named('copySh').get().mustRunAfter(tasks.named('build'))

	doLast {
		println "Release готов в build/libs/"
		println "Файлы: ${fileTree('build/libs/').files.collect { it.name }}"
		println "Запуск: cd build/libs && ./run.sh"
	}
}