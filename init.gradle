allprojects {
    repositories {
        maven {
          url "https://repo-mirror.mos.ru/repository/maven-public/"
        }
        gradlePluginPortal {
          url "https://repo-mirror.mos.ru/repository/maven-public/"
        }
    }
    buildscript {
            repositories {
                maven {
                    url = 'https://repo-mirror.mos.ru/repository/maven-public/'
                    }
            }
    }
}
    settingsEvaluated { settings ->
    settings.pluginManagement {
        repositories {
            mavenLocal()
            maven {
                url "https://repo-mirror.mos.ru/repository/maven-public/"
                }
            }
        }
    }
