package ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

@Getter
@Setter
@RequiredArgsConstructor
@FieldNameConstants
@Entity
public class SportKindRef extends RefWithParentEntity {

    @Column(name = "code_type_sport")
    private String codeTypeSport;

    @Column(name = "is_archive", nullable = false)
    private Boolean isArchive;
}
