<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
		"-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
		"https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
  Checkstyle configuration that checks the sun coding conventions from:
    - the Java Language Specification at
      https://docs.oracle.com/javase/specs/jls/se11/html/index.html
    - the Sun Code Conventions at https://www.oracle.com/technetwork/java/codeconvtoc-136057.html
    - the Javadoc guidelines at
      https://www.oracle.com/technetwork/java/javase/documentation/index-137868.html
    - the JDK Api documentation https://docs.oracle.com/en/java/javase/11/
    - some best practices
  Checkstyle is very configurable. Be sure to read the documentation at
  https://checkstyle.org (or in your downloaded distribution).
  Most Checks are configurable, be sure to consult the documentation.
  To completely disable a check, just comment it out or delete it from the file.
  To suppress certain violations please review suppression filters.
  Finally, it is worth reading the documentation.
-->

<module name="Checker">

	<property name="charset" value="UTF-8"/>

	<!--
		If you set the basedir property below, then all reported file
		names will be relative to the specified directory. See
		https://checkstyle.org/5.x/config.html#Checker
		<property name="basedir" value="${basedir}"/>
	-->
	<property name="severity" value="error"/>

	<property name="fileExtensions" value="java, properties, xml"/>
	<property name="tabWidth" value="4"/>

	<!-- Excludes all 'module-info.java' files              -->
	<!-- See https://checkstyle.org/config_filefilters.html -->
	<module name="BeforeExecutionExclusionFileFilter">
		<property name="fileNamePattern" value="module\-info\.java$"/>
	</module>

	<!-- https://checkstyle.org/config_filters.html#SuppressionFilter -->
	<module name="SuppressionFilter">
		<property name="file" value="${org.checkstyle.sun.suppressionfilter.config}"
				  default="checkstyle-suppressions.xml"/>
		<property name="optional" value="true"/>
	</module>

	<!-- Checks that a package-info.java file exists for each package.     -->
	<!-- See https://checkstyle.org/config_javadoc.html#JavadocPackage -->
	<!--	<module name="JavadocPackage"/>-->

	<!-- Checks whether files end with a new line.                        -->
	<!-- See https://checkstyle.org/config_misc.html#NewlineAtEndOfFile -->
	<!--	<module name="NewlineAtEndOfFile"/>-->

	<!-- Checks that property files contain the same keys.         -->
	<!-- See https://checkstyle.org/config_misc.html#Translation -->
	<module name="Translation"/>

	<!-- Checks for Size Violations.                    -->
	<!-- See https://checkstyle.org/config_sizes.html -->
	<module name="FileLength"/>
	<module name="LineLength">
		<property name="fileExtensions" value="java"/>
		<property name="max" value="120"/>
	</module>

	<!-- Checks for whitespace                               -->
	<!-- See https://checkstyle.org/config_whitespace.html -->
	<!--	<module name="FileTabCharacter"/>-->

	<!-- Miscellaneous other checks.                   -->
	<!-- See https://checkstyle.org/config_misc.html -->
	<module name="RegexpSingleline">
		<property name="format" value="\s+$"/>
		<property name="minimum" value="0"/>
		<property name="maximum" value="0"/>
		<property name="message" value="Line has trailing spaces."/>
	</module>

	<!-- Checks for Headers                                -->
	<!-- See https://checkstyle.org/config_header.html   -->
	<!-- <module name="Header"> -->
	<!--   <property name="headerFile" value="${checkstyle.header.file}"/> -->
	<!--   <property name="fileExtensions" value="java"/> -->
	<!-- </module> -->

	<module name="TreeWalker">

		<!-- Checks for Javadoc comments.                     -->
		<!-- See https://checkstyle.org/config_javadoc.html -->
		<module name="InvalidJavadocPosition"/>
		<module name="JavadocMethod"/>
		<module name="JavadocType"/>
		<!--		<module name="JavadocVariable"/>-->
		<module name="JavadocStyle"/>
			<module name="MissingJavadocMethod">
				<!--	<property name="minLineCount" value="3"/>-->
		</module>

		<!-- Checks for Naming Conventions.                  -->
		<!-- See https://checkstyle.org/config_naming.html -->
		<module name="ConstantName"/>
		<module name="LocalFinalVariableName"/>
		<module name="LocalVariableName"/>
		<module name="MemberName"/>
		<module name="MethodName"/>
		<module name="PackageName"/>
		<module name="ParameterName"/>
		<module name="StaticVariableName"/>
		<module name="TypeName"/>

		<!-- Checks for imports                              -->
		<!-- See https://checkstyle.org/config_import.html -->
		<module name="AvoidStarImport"/>
		<module name="IllegalImport"/> <!-- defaults to sun.* packages -->
		<module name="RedundantImport"/>
		<module name="UnusedImports">
			<property name="processJavadoc" value="false"/>
		</module>

		<!-- Checks for Size Violations.                    -->
		<!-- See https://checkstyle.org/config_sizes.html -->
		<module name="MethodLength"/>
		<module name="ParameterNumber">
			<property name="max" value="21"/>
			<property name="ignoreOverriddenMethods" value="true"/>
			<property name="tokens" value="METHOD_DEF"/>
		</module>

		<!-- Checks for whitespace                               -->
		<!-- See https://checkstyle.org/config_whitespace.html -->
		<module name="EmptyForIteratorPad"/>
		<module name="GenericWhitespace"/>
		<module name="MethodParamPad"/>
		<module name="NoWhitespaceAfter"/>
		<module name="NoWhitespaceBefore"/>
		<module name="OperatorWrap">
			<property name="tokens"
					  value="QUESTION, COLON, EQUAL, NOT_EQUAL, DIV, MINUS, STAR, MOD, SR, BSR, GE, GT, SL, LE, LT,
					  BXOR, BOR, LOR, BAND, LAND, TYPE_EXTENSION_AND, LITERAL_INSTANCEOF"/>
		</module>
		<module name="ParenPad"/>
		<module name="TypecastParenPad"/>
		<module name="WhitespaceAfter"/>
		<module name="WhitespaceAround"/>
		<module name="SingleSpaceSeparator"/>

		<!-- Modifier Checks                                    -->
		<!-- See https://checkstyle.org/config_modifiers.html -->
		<module name="ModifierOrder"/>
		<module name="RedundantModifier"/>

		<!-- Checks for blocks. You know, those {}'s         -->
		<!-- See https://checkstyle.org/config_blocks.html -->
		<module name="AvoidNestedBlocks"/>
		<module name="EmptyBlock"/>
		<module name="LeftCurly"/>
		<module name="NeedBraces"/>
		<module name="RightCurly"/>

		<!-- Checks for common coding problems               -->
		<!-- See https://checkstyle.org/config_coding.html -->
		<module name="EmptyStatement"/>
		<module name="EqualsHashCode"/>
<!--		<module name="HiddenField"/>-->
		<module name="IllegalInstantiation"/>
		<module name="InnerAssignment"/>
		<module name="MissingSwitchDefault"/>
		<module name="MultipleVariableDeclarations"/>
		<module name="SimplifyBooleanExpression"/>
		<module name="SimplifyBooleanReturn"/>

		<!-- Checks for class design                         -->
		<!-- See https://checkstyle.org/config_design.html -->
		<!--		<module name="DesignForExtension"/>-->
		<!--		<module name="FinalClass"/>-->
		<!--		<module name="HideUtilityClassConstructor"/>-->
		<module name="InterfaceIsType"/>
		<module name="VisibilityModifier">
			<property name="protectedAllowed" value="true"/>
		</module>

		<!-- Miscellaneous other checks.                   -->
		<!-- See https://checkstyle.org/config_misc.html -->
		<module name="ArrayTypeStyle"/>
		<!--		<module name="FinalParameters"/>-->
		<!--		<module name="TodoComment"/>-->
		<module name="UpperEll"/>

		<!-- https://checkstyle.org/config_filters.html#SuppressionXpathFilter -->
		<module name="SuppressionXpathFilter">
			<property name="file" value="${org.checkstyle.sun.suppressionxpathfilter.config}"
					  default="checkstyle-xpath-suppressions.xml"/>
			<property name="optional" value="true"/>
		</module>

	</module>

</module>