package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.stereotype.Service;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.Employment;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.KafkaMessageProcessed;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.error.Errors;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.repository.EmploymentRepository;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.AdditionalEducationService;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.EmploymentMapperService;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.LogService;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.ValidationService;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation.dto.KafkaMessage;

import java.util.List;

@Service
@RequiredArgsConstructor
public class AdditionalEducationServiceImpl implements AdditionalEducationService {
    private final EmploymentRepository employmentRepository;
    private final ObjectMapper objectMapper;

    private final ValidationService validationService;
    private final EmploymentMapperService employmentMapperService;
    private final LogService logService;

    @Override
    public void process(@NonNull GenericMessage<String> message) throws JsonProcessingException {
        KafkaMessageProcessed kafkaMessageProcessed = logService.buildKafkaMessageProcessed(message);
        KafkaMessage parsedMessage = objectMapper.readValue(message.getPayload(), KafkaMessage.class);

        validationService.validateFields(parsedMessage, kafkaMessageProcessed.getId());

        List<Employment> currentEmployments = employmentRepository.findAllByPersonIdAndEventIdAndIsDelete(
                parsedMessage.personId(), parsedMessage.eventId(), false);

        if (currentEmployments.isEmpty()) {
            Employment employment = employmentMapperService.messageToEntityNew(parsedMessage);
            employmentRepository.save(employment);

            logService.buildKafkaLogOk(parsedMessage.personId(), employment, kafkaMessageProcessed.getId());
        }

        logService.buildKafkaLogFail(currentEmployments.isEmpty(), Errors.E750,
                parsedMessage.personId(), kafkaMessageProcessed.getId());
    }
}
