package ru.mos.school.student.portfolio_fct_additional_education_attendence.service.additionalEducation;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.NonNull;
import org.springframework.messaging.support.GenericMessage;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.AbstractEntity;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.KafkaMessageProcessed;
import ru.mos.school.student.portfolio_fct_additional_education_attendence.model.error.Errors;

public interface LogService {
    /**
     * Формирование и сохранение сообщения из топика в БД.
     * @param message сырое сообщение.
     * @return сохраненное в БД сообщение.
     * @throws JsonProcessingException ошибка формирования Json
     */
    KafkaMessageProcessed buildKafkaMessageProcessed(@NonNull GenericMessage<String> message)
            throws JsonProcessingException;

    /**
     * Формирование и сохранение в БД и отправка в лог сообщения об успешной обработке и сохранении сообщения.
     * @param personId personId
     * @param entity сохраненняемая сущность
     * @param kafkaMessageProcessedId kafkaMessageProcessedId
     * @param <T> сохраненняемая сущность
     */
    <T extends AbstractEntity> void buildKafkaLogOk(String personId, T entity, Long kafkaMessageProcessedId);

    /**
     * Формирование и сохранение в БД и отправка в лог сообщения об неуспешной обработке и сохранении сообщения.
     * @param isTrue результат проверки
     * @param error тип ошибки
     * @param personId personId
     * @param kafkaMessageProcessedId kafkaMessageProcessedId
     */
    void buildKafkaLogFail(Boolean isTrue, Errors error, String personId, Long kafkaMessageProcessedId);
}
