package ru.mos.school.student.portfolio_fct_additional_education_attendence.model.ref;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@RequiredArgsConstructor
@Entity
public class SectionRef extends RefWithParentEntity {
    @Column(name = "is_archive", nullable = false)
    private Boolean isArchive;
    @Column(name = "short_value")
    private String shortValue;
}
