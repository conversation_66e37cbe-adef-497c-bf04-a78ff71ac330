server:
  servlet:
    contextPath: /${spring.application.name}
  port: ${SERVER_PORT:8080}

spring:
  application:
    name: additional-education
  datasource:
    originUrl: ${DATASOURCE_HOST:localhost}
    originPort: ${DATASOURCE_PORT:5432}
    url: jdbc:postgresql://${spring.datasource.originUrl}:${spring.datasource.originPort}/${DATASOURCE_DB:portfolio}
    username: ${DATASOURCE_USERNAME:portfolio}
    password: ${DATASOURCE_PASSWORD:portfolio}
    driver-class-name: org.postgresql.Driver
    hikari:
      schema: ${DATASOURCE_SCHEMA:portfolio}
  jpa:
    properties:
      hibernate:
        default_schema: ${DATASOURCE_SCHEMA:portfolio}
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      naming:
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyComponentPathImpl
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
      ddl-auto: validate
    show-sql: false

logging:
  level:
    root: info

management:
  endpoint:
    prometheus:
      access: read_only
  endpoints:
    web:
      exposure:
        include: health, prometheus
  prometheus:
    metrics:
      export:
        enabled: true

additional-education:
  kafka:
    enable: ${KAFKA_ENABLE:false}
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    topics: ${KAFKA_TOPICS:education.fct.additional-education-attendence.0}
    group-id: ${KAFKA_GROUP_ID:portfolio_group}
    properties-security-protocol: SASL_PLAINTEXT
    properties-sasl-mechanism: PLAIN
    properties-sasl-jaas-config: >-
      org.apache.kafka.common.security.scram.ScramLoginModule required
      username="${KAFKA_USERNAME:portfolio}" 
      password="${KAFKA_PASSWORD:portfolio}";
    max-poll-interval-ms: ${KAFKA_MAX_POLL_INTERVAL:300000}
    max-poll-records: ${KAFKA_MAX_POLL_RECORDS:500}